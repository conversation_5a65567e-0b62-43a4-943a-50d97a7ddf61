'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, notFound, useRouter } from 'next/navigation';
import { useOptimizedLiveFixtures } from '@/hooks/useOptimizedSocket';
import FixtureHeader from '@/components/fixture/FixtureHeader';
import FixtureContent from '@/components/fixture/FixtureContent';
import FixtureSidebar from '@/components/fixture/FixtureSidebar';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import {
  FixturePageData,
  MatchStatistics,
  MatchEvents,
  MatchLineups,
  HeadToHeadData,
  FixturePredictions
} from '@/types/fixture';

export default function FixturePage() {
  const params = useParams();
  const router = useRouter();
  const [fixtureData, setFixtureData] = useState<FixturePageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('facts');

  // WebSocket for live updates
  const { liveFixtures, connected } = useOptimizedLiveFixtures();

  // Debug WebSocket connection
  useEffect(() => {
    console.log('WebSocket connected:', connected);
    console.log('Live fixtures count:', liveFixtures.length);
    if (liveFixtures.length > 0) {
      console.log('Live fixtures:', liveFixtures.map(f => ({ id: f._id, status: f.fixture.status.short, teams: `${f.teams.home.name} vs ${f.teams.away.name}` })));
    }
  }, [connected, liveFixtures]);

  // Parse URL slug to extract fixture ID
  const parseSlug = (slug: string[]): { fixtureId: number; teamSlug: string } | null => {
    if (!slug || slug.length < 2) return null;

    // Last segment should be the fixture ID
    const fixtureId = parseInt(slug[slug.length - 1]);
    if (isNaN(fixtureId)) return null;

    // Everything before the last segment is the team slug
    const teamSlug = slug.slice(0, -1).join('/');

    return { fixtureId, teamSlug };
  };

  // Generate correct team slug from fixture data
  const generateTeamSlug = (fixture: { teams: { home: { name: string }; away: { name: string } } }): string => {
    const homeTeam = fixture.teams.home.name.toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');
    const awayTeam = fixture.teams.away.name.toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');

    return `${homeTeam}-vs-${awayTeam}`;
  };



  useEffect(() => {
    const fetchFixtureData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Parse the URL slug
        const slugArray = Array.isArray(params.slug) ? params.slug : [params.slug].filter((s): s is string => Boolean(s));
        const parsedSlug = parseSlug(slugArray);
        
        if (!parsedSlug) {
          notFound();
        }

        const { fixtureId } = parsedSlug;

        // Fetch main fixture data
        const fixtureResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures?id=${fixtureId}`, {
          headers: {
            'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756'
          }
        });

        if (!fixtureResponse.ok) {
          if (fixtureResponse.status === 404) {
            notFound();
          }
          throw new Error('Failed to fetch fixture data');
        }

        const fixtures = await fixtureResponse.json();
        const fixture = Array.isArray(fixtures) ? fixtures[0] : fixtures;

        if (!fixture) {
          notFound();
        }

        // Validate URL team slug matches actual teams and redirect if necessary
        const expectedSlug = generateTeamSlug(fixture);
        const { teamSlug: currentSlug } = parsedSlug;

        if (currentSlug !== expectedSlug) {
          // Redirect to correct URL with proper team names
          const correctUrl = `/match/${expectedSlug}/${fixtureId}`;
          router.replace(correctUrl);
          return; // Exit early to prevent setting state before redirect
        }

        // Fetch additional data based on match status
        const dataPromises: Promise<unknown>[] = [];
        
        // Always try to fetch these (they may or may not be available)
        dataPromises.push(
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures/${fixtureId}/statistics`, {
            headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
          }).then(res => res.ok ? res.json() : null).catch(() => null)
        );

        dataPromises.push(
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures/${fixtureId}/events`, {
            headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
          }).then(res => res.ok ? res.json() : null).catch(() => null)
        );

        dataPromises.push(
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures/${fixtureId}/lineups`, {
            headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
          }).then(res => res.ok ? res.json() : null).catch(() => null)
        );

        // Head-to-head data
        dataPromises.push(
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures/h2h?h2h=${fixture.teams.home.id}-${fixture.teams.away.id}`, {
            headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
          }).then(res => res.ok ? res.json() : null).catch(() => null)
        );

        // Predictions data
        dataPromises.push(
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/predictions?fixture=${fixtureId}`, {
            headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
          }).then(res => res.ok ? res.json() : null).catch(() => null)
        );

        const [statistics, events, lineups, headToHead, predictions] = await Promise.all(dataPromises);

        setFixtureData({
          fixture,
          statistics: statistics as MatchStatistics | undefined,
          events: events as MatchEvents | undefined,
          lineups: lineups as MatchLineups | undefined,
          headToHead: headToHead as HeadToHeadData | undefined,
          predictions: predictions as FixturePredictions | undefined
        });

        // Set default active tab based on match status
        if (fixture.fixture.status.short === 'NS') {
          setActiveTab('preview');
        } else {
          setActiveTab('facts');
        }

      } catch (err) {
        console.error('Error fetching fixture data:', err);
        setError('Failed to load fixture data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchFixtureData();
  }, [params.slug, router]);

  // Handle live fixture updates via WebSocket (same as homepage)
  useEffect(() => {
    if (liveFixtures.length > 0 && fixtureData) {
      console.log('Checking for live updates...');
      console.log('Current fixture ID:', fixtureData.fixture._id);
      console.log('Current fixture API ID:', fixtureData.fixture.fixture.id);
      console.log('Live fixture IDs:', liveFixtures.map(f => ({ _id: f._id, apiId: f.fixture.id })));

      // Find if our current fixture is in the live updates
      const liveFixture = liveFixtures.find(f =>
        f._id === fixtureData.fixture._id ||
        f.fixture.id === fixtureData.fixture.fixture.id ||
        f._id === fixtureData.fixture.fixture.id || // Sometimes _id matches API ID
        f.fixture.id === fixtureData.fixture._id // Sometimes API ID matches _id
      );

      if (liveFixture) {
        console.log('✅ Live fixture update received:', liveFixture);

        // Debug: Log what data is available in the WebSocket update
        console.log('WebSocket data contains:', {
          hasEvents: !!(liveFixture.events && liveFixture.events.length > 0),
          hasStatistics: !!(liveFixture.statistics && liveFixture.statistics.length > 0),
          hasLineups: !!(liveFixture.lineups && liveFixture.lineups.length > 0),
          hasPlayers: !!(liveFixture.players && liveFixture.players.length > 0),
          eventsCount: liveFixture.events?.length || 0,
          statisticsCount: liveFixture.statistics?.length || 0,
          lineupsCount: liveFixture.lineups?.length || 0,
          playersCount: liveFixture.players?.length || 0
        });

        // Check if there are significant changes (goals, status, time)
        const hasGoalUpdate = liveFixture.goals.home !== fixtureData.fixture.goals.home ||
                             liveFixture.goals.away !== fixtureData.fixture.goals.away;
        const hasStatusUpdate = liveFixture.fixture.status.short !== fixtureData.fixture.fixture.status.short ||
                               liveFixture.fixture.status.elapsed !== fixtureData.fixture.fixture.status.elapsed;

        // Only update if there are actual changes to prevent infinite loops
        if (hasGoalUpdate || hasStatusUpdate ||
            liveFixture.fixture.status.elapsed !== fixtureData.fixture.fixture.status.elapsed) {

          // 🚀 NEW: Update ALL data from WebSocket (not just basic fixture data)
          setFixtureData(prev => {
            if (!prev) return null;

            return {
              ...prev,
              fixture: liveFixture,
              // Update events, statistics, lineups if available in WebSocket data
              // Cast to the expected types since WebSocket data structure matches our interfaces
              events: liveFixture.events && liveFixture.events.length > 0 ?
                liveFixture.events as import('@/types/fixture').MatchEvents : prev.events,
              statistics: liveFixture.statistics && liveFixture.statistics.length > 0 ?
                liveFixture.statistics as import('@/types/fixture').MatchStatistics : prev.statistics,
              lineups: liveFixture.lineups && liveFixture.lineups.length > 0 ?
                liveFixture.lineups as import('@/types/fixture').MatchLineups : prev.lineups,
            };
          });

          // Only refetch if WebSocket data doesn't contain the detailed information
          const needsRefetch = (hasGoalUpdate || hasStatusUpdate) &&
                             (!liveFixture.events || liveFixture.events.length === 0 ||
                              !liveFixture.statistics || liveFixture.statistics.length === 0);

          if (needsRefetch) {
            console.log('WebSocket data incomplete, refetching events and statistics...');
            refetchLiveData(liveFixture.fixture.id);
          } else {
            console.log('✅ Using complete data from WebSocket - no API refetch needed!');
          }
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [liveFixtures]); // 🔧 FIXED: Intentionally excluding fixtureData to prevent infinite loop

  // Function to refetch live data (events, statistics) - memoized to prevent unnecessary re-renders
  const refetchLiveData = useCallback(async (fixtureId: number) => {
    try {
      // Fetch updated events
      const eventsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures/${fixtureId}/events`, {
        headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
      });

      if (eventsResponse.ok) {
        const newEvents = await eventsResponse.json();
        setFixtureData(prev => prev ? { ...prev, events: newEvents } : null);
      }

      // Fetch updated statistics
      const statsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures/${fixtureId}/statistics`, {
        headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
      });

      if (statsResponse.ok) {
        const newStats = await statsResponse.json();
        setFixtureData(prev => prev ? { ...prev, statistics: newStats } : null);
      }
    } catch (error) {
      console.error('Error refetching live data:', error);
    }
  }, []); // Empty dependency array since this function doesn't depend on any props or state

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex justify-center items-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !fixtureData) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex justify-center items-center min-h-[60vh]">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-foreground mb-4">Error Loading Match</h1>
            <p className="text-muted-foreground mb-4">{error || 'Match not found'}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90"
            >
              Try Again
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />



      <div className="flex justify-center px-0 md:px-4">
        <div className="flex max-w-7xl w-full">
          {/* Main Content */}
          <div className="flex-1 min-w-0 px-0 md:px-4 lg:px-0 lg:pr-4 pt-4">
            <div className="space-y-4 md:space-y-6">
              {/* SEO H1 Title - Hidden visually but accessible to search engines */}
              <h1 className="sr-only">
                {fixtureData.fixture.teams.home.name} vs {fixtureData.fixture.teams.away.name} live score, H2H, standings and prediction
              </h1>

              {/* Fixture Header with Navigation Tabs */}
              <FixtureHeader
                fixture={fixtureData.fixture}
                activeTab={activeTab}
                onTabChange={setActiveTab}
              />

              {/* Tab Content */}
              <FixtureContent
                activeTab={activeTab}
                fixtureData={fixtureData}
              />
            </div>
          </div>

          {/* Sidebar */}
          <div className="hidden lg:block">
            <FixtureSidebar
              fixture={fixtureData.fixture}
              predictions={fixtureData.predictions}
            />
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
