'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import SocketManager from '@/lib/socketManager';
import { Fixture } from '@/lib/api';

// Optimized hook for live fixtures with memory management
export function useOptimizedLiveFixtures() {
  const [liveFixtures, setLiveFixtures] = useState<Fixture[]>([]);
  const [connected, setConnected] = useState(false);
  const socketManager = useRef<SocketManager | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    socketManager.current = SocketManager.getInstance();
    
    const initializeConnection = async () => {
      try {
        await socketManager.current!.connect();
        setConnected(true);
        
        // Subscribe to live fixtures with memory-efficient callback
        const unsubscribe = socketManager.current!.subscribe(
          'live-fixtures-update',
          (data: unknown) => {
            // ✅ Type guard and limit the number of fixtures to prevent memory bloat
            if (Array.isArray(data)) {
              const fixtures = data as Fixture[];
              const limitedFixtures = fixtures.slice(0, 50); // Limit to 50 live fixtures
              setLiveFixtures(limitedFixtures);
            }
          }
        );
        
        unsubscribeRef.current = unsubscribe;
        socketManager.current!.subscribeToLiveFixtures();
        
      } catch (error) {
        console.error('Failed to connect to socket:', error);
        setConnected(false);
      }
    };

    initializeConnection();

    // Cleanup function
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
      if (socketManager.current) {
        socketManager.current.unsubscribeFromLiveFixtures();
      }
      // ✅ Clear state to free memory
      setLiveFixtures([]);
    };
  }, []);

  return {
    liveFixtures,
    connected,
  };
}

// Optimized hook for specific fixture updates
export function useOptimizedFixtureSocket(fixtureId: number | null) {
  const [fixture, setFixture] = useState<Fixture | null>(null);
  const [connected, setConnected] = useState(false);
  const socketManager = useRef<SocketManager | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);
  const currentFixtureId = useRef<number | null>(null);

  const subscribeToFixture = useCallback(async (id: number) => {
    if (!socketManager.current) return;

    try {
      await socketManager.current.connect();
      setConnected(true);

      // Unsubscribe from previous fixture if any
      if (currentFixtureId.current && currentFixtureId.current !== id) {
        socketManager.current.unsubscribeFromFixture(currentFixtureId.current);
      }

      // Subscribe to new fixture
      const unsubscribe = socketManager.current.subscribe(
        'fixture-update',
        (data: unknown) => {
          // ✅ Type guard for fixture data
          if (data && typeof data === 'object' && 'fixture' in data) {
            const updatedFixture = data as Fixture;
            if (updatedFixture.fixture.id === id) {
              setFixture(updatedFixture);
            }
          }
        }
      );

      unsubscribeRef.current = unsubscribe;
      socketManager.current.subscribeToFixture(id);
      currentFixtureId.current = id;

    } catch (error) {
      console.error('Failed to subscribe to fixture:', error);
      setConnected(false);
    }
  }, []);

  useEffect(() => {
    socketManager.current = SocketManager.getInstance();

    if (fixtureId) {
      subscribeToFixture(fixtureId);
    }

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
      if (currentFixtureId.current && socketManager.current) {
        socketManager.current.unsubscribeFromFixture(currentFixtureId.current);
      }
      // ✅ Clear state to free memory
      setFixture(null);
      currentFixtureId.current = null;
    };
  }, [fixtureId, subscribeToFixture]);

  return {
    fixture,
    connected,
  };
}

// Hook for managing socket connection status globally
export function useSocketConnection() {
  const [connected, setConnected] = useState(false);
  const socketManager = useRef<SocketManager | null>(null);

  useEffect(() => {
    socketManager.current = SocketManager.getInstance();
    
    const checkConnection = () => {
      setConnected(socketManager.current?.isConnected() || false);
    };

    // Check initial connection
    checkConnection();

    // Set up periodic connection check (lightweight)
    const interval = setInterval(checkConnection, 5000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  const reconnect = useCallback(async () => {
    if (socketManager.current) {
      try {
        await socketManager.current.connect();
        setConnected(true);
      } catch (error) {
        console.error('Failed to reconnect:', error);
        setConnected(false);
      }
    }
  }, []);

  return {
    connected,
    reconnect,
  };
}
