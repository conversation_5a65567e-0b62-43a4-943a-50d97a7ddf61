'use client';

import { useMemo, useState, useCallback } from 'react';
import { Fixture } from '@/lib/api';
import { groupFixturesByLeague, LeagueGroup } from '@/lib/leagueTiers';

interface VirtualizedFixturesOptions {
  fixtures: Fixture[];
  itemsPerPage?: number;
  maxVisibleLeagues?: number;
}

interface VirtualizedFixturesResult {
  visibleFixtures: Fixture[];
  visibleLeagues: LeagueGroup[];
  currentPage: number;
  totalPages: number;
  hasMore: boolean;
  loadMore: () => void;
  reset: () => void;
  totalFixtures: number;
}

// ✅ Hook to virtualize large fixture lists and prevent memory bloat
export function useVirtualizedFixtures({
  fixtures,
  itemsPerPage = 50, // Limit fixtures per page
  maxVisibleLeagues = 10, // Limit visible leagues
}: VirtualizedFixturesOptions): VirtualizedFixturesResult {
  const [currentPage, setCurrentPage] = useState(1);

  // ✅ Memoize expensive operations
  const { paginatedFixtures, leagueGroups, totalPages } = useMemo(() => {
    if (!fixtures.length) {
      return {
        paginatedFixtures: [],
        leagueGroups: [],
        totalPages: 0,
      };
    }

    // Calculate pagination
    const startIndex = 0;
    const endIndex = currentPage * itemsPerPage;
    const paginatedFixtures = fixtures.slice(startIndex, endIndex);
    
    // Group fixtures by league (only for visible fixtures)
    const leagueGroups = groupFixturesByLeague(paginatedFixtures);
    
    // Limit number of visible leagues to prevent memory issues
    const limitedLeagueGroups = leagueGroups.slice(0, maxVisibleLeagues);
    
    const totalPages = Math.ceil(fixtures.length / itemsPerPage);

    return {
      paginatedFixtures,
      leagueGroups: limitedLeagueGroups,
      totalPages,
    };
  }, [fixtures, currentPage, itemsPerPage, maxVisibleLeagues]);

  const loadMore = useCallback(() => {
    if (currentPage < totalPages) {
      setCurrentPage(prev => prev + 1);
    }
  }, [currentPage, totalPages]);

  const reset = useCallback(() => {
    setCurrentPage(1);
  }, []);

  const hasMore = currentPage < totalPages;

  return {
    visibleFixtures: paginatedFixtures,
    visibleLeagues: leagueGroups,
    currentPage,
    totalPages,
    hasMore,
    loadMore,
    reset,
    totalFixtures: fixtures.length,
  };
}

// ✅ Hook for memory-efficient fixture filtering
export function useOptimizedFixtureFilter(
  fixtures: Fixture[],
  searchQuery: string,
  statusFilter: 'all' | 'live' | 'finished' | 'upcoming'
) {
  return useMemo(() => {
    if (!fixtures.length) return [];

    let filtered = fixtures;

    // ✅ Apply status filter first (most selective)
    if (statusFilter !== 'all') {
      filtered = filtered.filter(fixture => {
        switch (statusFilter) {
          case 'live':
            return ['1H', '2H', 'HT', 'ET', 'BT', 'P', 'SUSP', 'INT'].includes(fixture.fixture.status.short);
          case 'finished':
            return ['FT', 'AET', 'PEN', 'PST', 'CANC', 'ABD', 'AWD', 'WO'].includes(fixture.fixture.status.short);
          case 'upcoming':
            return ['TBD', 'NS'].includes(fixture.fixture.status.short);
          default:
            return true;
        }
      });
    }

    // ✅ Apply search filter (if query is long enough to be meaningful)
    if (searchQuery.trim().length >= 2) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(fixture =>
        fixture.teams.home.name.toLowerCase().includes(query) ||
        fixture.teams.away.name.toLowerCase().includes(query) ||
        fixture.league.name.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [fixtures, searchQuery, statusFilter]);
}

// ✅ Hook for memory-efficient league expansion state
export function useLeagueExpansion(initialExpanded: Set<number> = new Set()) {
  const [expandedLeagues, setExpandedLeagues] = useState(initialExpanded);

  const toggleLeague = useCallback((leagueId: number) => {
    setExpandedLeagues(prev => {
      const newSet = new Set(prev);
      if (newSet.has(leagueId)) {
        newSet.delete(leagueId);
      } else {
        newSet.add(leagueId);
      }
      return newSet;
    });
  }, []);

  const expandAll = useCallback((leagueIds: number[]) => {
    setExpandedLeagues(new Set(leagueIds));
  }, []);

  const collapseAll = useCallback(() => {
    setExpandedLeagues(new Set());
  }, []);

  return {
    expandedLeagues,
    toggleLeague,
    expandAll,
    collapseAll,
  };
}
