'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import MainContent from '@/components/MainContent';
import RightSidebar from '@/components/RightSidebar';
import { LazyMobileBottomNav, LazyMobileLeaguesView, LazyMobileNewsView } from '@/components/LazyComponents';
import PerformanceMonitor from '@/components/PerformanceMonitor';

interface PageClientWrapperProps {
  children?: React.ReactNode;
}

export function PageClientWrapper({ children }: PageClientWrapperProps) {
  const [selectedLeagueId, setSelectedLeagueId] = useState<number | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isRightSidebarOpen, setIsRightSidebarOpen] = useState(false);
  const [mobileActiveTab, setMobileActiveTab] = useState<'matches' | 'leagues' | 'news'>('matches');

  // Handle responsive sidebars
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsSidebarOpen(false);
        setIsRightSidebarOpen(false);
      } else if (window.innerWidth < 1200) {
        setIsSidebarOpen(true);
        setIsRightSidebarOpen(false);
      } else {
        setIsSidebarOpen(true);
        setIsRightSidebarOpen(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleLeagueSelect = (leagueId: number) => {
    setSelectedLeagueId(selectedLeagueId === leagueId ? null : leagueId);
    // On mobile, switch back to matches view after league selection
    if (typeof window !== 'undefined' && window.innerWidth < 768) {
      setMobileActiveTab('matches');
    }
  };

  const handleClearLeagueFilter = () => {
    setSelectedLeagueId(null);
  };

  const toggleRightSidebar = () => {
    setIsRightSidebarOpen(!isRightSidebarOpen);
  };

  return (
    <>
      {/* Header with interactive functionality */}
      <Header onToggleRightSidebar={toggleRightSidebar} />

      <div className="flex justify-center px-0 md:px-4">
        <div className="flex max-w-7xl w-full">
          {/* Left Sidebar */}
          <div className={`${isSidebarOpen ? 'block' : 'hidden'} md:block`}>
            <Sidebar
              onLeagueSelect={handleLeagueSelect}
              selectedLeagueId={selectedLeagueId}
            />
          </div>

      {/* Main Content */}
      <div className="flex-1 min-w-0">
        {/* Desktop: Always show MainContent */}
        <div className="hidden md:block">
          <MainContent
            selectedLeagueId={selectedLeagueId}
            onClearLeagueFilter={handleClearLeagueFilter}
          />
        </div>

        {/* Mobile: Show different views based on active tab */}
        <div className="md:hidden">
          {mobileActiveTab === 'matches' && (
            <MainContent
              selectedLeagueId={selectedLeagueId}
              onClearLeagueFilter={handleClearLeagueFilter}
            />
          )}
          {mobileActiveTab === 'leagues' && (
            <LazyMobileLeaguesView
              onLeagueSelect={handleLeagueSelect}
              selectedLeagueId={selectedLeagueId}
            />
          )}
          {mobileActiveTab === 'news' && <LazyMobileNewsView />}
        </div>

        {/* Custom children content */}
        {children}
      </div>

          {/* Right Sidebar */}
          <div className={`${isRightSidebarOpen ? 'block' : 'hidden'} md:block`}>
            <RightSidebar />
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      <div className="md:hidden">
        <LazyMobileBottomNav
          activeTab={mobileActiveTab}
          onTabChange={(tabId: string) => setMobileActiveTab(tabId as 'matches' | 'leagues' | 'news')}
          accentColor="#1A3050"
        />
      </div>

      {/* Performance Monitoring */}
      <PerformanceMonitor />
    </>
  );
}
