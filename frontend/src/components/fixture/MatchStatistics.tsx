'use client';

import { Fixture } from '@/lib/api';
import type { MatchStatistics, TeamStatistics, StatItem, MatchLineups } from '@/types/fixture';


interface MatchStatisticsProps {
  statistics?: MatchStatistics;
  fixture: Fixture;
  lineups?: MatchLineups;
}

interface StatConfig {
  key: string;
  label: string;
  isPercentage: boolean;
}

export default function MatchStatistics({ statistics, fixture }: MatchStatisticsProps) {

  if (!statistics || statistics.length === 0) {
    return (
      <div className="bg-card rounded-lg border container-border p-6 mx-2 md:mx-0">
        <h3 className="text-lg font-semibold mb-4">Match Statistics</h3>
        <p className="text-muted-foreground">Statistics not available for this match.</p>
      </div>
    );
  }

  // Parse statistics data
  const homeStats = statistics.find((stat: TeamStatistics) => stat.team.id === fixture.teams.home.id);
  const awayStats = statistics.find((stat: TeamStatistics) => stat.team.id === fixture.teams.away.id);

  if (!homeStats || !awayStats) {
    return (
      <div className="bg-card rounded-lg border container-border p-6 mx-2 md:mx-0">
        <h3 className="text-lg font-semibold mb-4">Match Statistics</h3>
        <p className="text-muted-foreground">Statistics data incomplete.</p>
      </div>
    );
  }

  // Helper function to get stat value
  const getStatValue = (stats: StatItem[], type: string): number => {
    const stat = stats.find(s => s.type === type);
    if (!stat || stat.value === null) return 0;
    
    // Handle percentage values
    if (typeof stat.value === 'string' && stat.value.includes('%')) {
      return parseInt(stat.value.replace('%', ''));
    }
    
    return typeof stat.value === 'number' ? stat.value : parseInt(stat.value.toString()) || 0;
  };



  // Simple color scheme for home/away stats
  const getTeamColors = (teamId: number) => {
    const colors = {
      home: { background: '#3399DB', text: '#ffffff' },
      away: { background: '#E84C3D', text: '#ffffff' }
    };
    return teamId === fixture.teams.home.id ? colors.home : colors.away;
  };

  // Helper function to render Ball possession bar with percentages inside
  const renderBallPossessionBar = (homeValue: number, awayValue: number) => {
    const homeColors = getTeamColors(fixture.teams.home.id);
    const awayColors = getTeamColors(fixture.teams.away.id);

    return (
      <div className="mb-6">
        {/* Label */}
        <div className="text-center text-sm text-muted-foreground mb-2">
          Ball possession
        </div>

        {/* Progress Bar with percentages inside */}
        <div className="flex h-8 rounded-full overflow-hidden">
          {/* Home team bar */}
          <div
            className="flex items-center justify-start pl-3 text-sm font-medium text-white"
            style={{
              width: `${homeValue}%`,
              backgroundColor: homeColors.background
            }}
          >
            {homeValue >= 15 && <span>{homeValue}%</span>}
          </div>
          {/* Away team bar */}
          <div
            className="flex items-center justify-end pr-3 text-sm font-medium text-white"
            style={{
              width: `${awayValue}%`,
              backgroundColor: awayColors.background
            }}
          >
            {awayValue >= 15 && <span>{awayValue}%</span>}
          </div>
        </div>
      </div>
    );
  };

  // Helper function to render simple stat row (value - label - value)
  const renderSimpleStatRow = (homeValue: number, awayValue: number, label: string, isPercentage = false) => {
    // Determine which value is higher for highlighting
    const homeIsHigher = homeValue > awayValue;
    const awayIsHigher = awayValue > homeValue;

    const homeColors = getTeamColors(fixture.teams.home.id);
    const awayColors = getTeamColors(fixture.teams.away.id);

    // Format display value - show decimals for xG
    const formatValue = (value: number) => {
      if (label.toLowerCase().includes('xg') || label.toLowerCase().includes('expected goals')) {
        return value.toFixed(2);
      }
      return isPercentage ? `${value}%` : value.toString();
    };

    return (
      <div key={label} className="mb-4">
        <div className="flex items-center justify-between mx-auto" style={{ maxWidth: 'var(--container-2x)' }}>
          {/* Home Value */}
          <div
            className="px-2 py-1 rounded-full text-sm font-medium min-w-[40px] text-center"
            style={homeIsHigher ? {
              backgroundColor: homeColors.background,
              color: homeColors.text
            } : {}}
          >
            <span className={homeIsHigher ? '' : 'text-foreground dark:text-white'}>
              {formatValue(homeValue)}
            </span>
          </div>

          {/* Label */}
          <div className="text-center text-sm text-muted-foreground flex-1 mx-4">
            {label}
          </div>

          {/* Away Value */}
          <div
            className="px-2 py-1 rounded-full text-sm font-medium min-w-[40px] text-center"
            style={awayIsHigher ? {
              backgroundColor: awayColors.background,
              color: awayColors.text
            } : {}}
          >
            <span className={awayIsHigher ? '' : 'text-foreground dark:text-white'}>
              {formatValue(awayValue)}
            </span>
          </div>
        </div>
      </div>
    );
  };

  // Helper function to render combined accurate passes (count + percentage)
  const renderAccuratePassesRow = (homeAccurate: number, awayAccurate: number, homeAccuracy: number, awayAccuracy: number) => {
    // Determine which value is higher for highlighting
    const homeIsHigher = homeAccurate > awayAccurate;
    const awayIsHigher = awayAccurate > homeAccurate;

    const homeColors = getTeamColors(fixture.teams.home.id);
    const awayColors = getTeamColors(fixture.teams.away.id);

    return (
      <div key="accurate-passes" className="mb-4">
        <div className="flex items-center justify-between mx-auto" style={{ maxWidth: 'var(--container-2x)' }}>
          {/* Home Value */}
          <div
            className="px-2 py-1 rounded-full text-sm font-medium min-w-[60px] text-center"
            style={homeIsHigher ? {
              backgroundColor: homeColors.background,
              color: homeColors.text
            } : {}}
          >
            <span className={homeIsHigher ? '' : 'text-foreground dark:text-white'}>
              {homeAccurate} ({homeAccuracy}%)
            </span>
          </div>

          {/* Label */}
          <div className="text-center text-sm text-muted-foreground flex-1 mx-4">
            Accurate passes
          </div>

          {/* Away Value */}
          <div
            className="px-2 py-1 rounded-full text-sm font-medium min-w-[60px] text-center"
            style={awayIsHigher ? {
              backgroundColor: awayColors.background,
              color: awayColors.text
            } : {}}
          >
            <span className={awayIsHigher ? '' : 'text-foreground dark:text-white'}>
              {awayAccurate} ({awayAccuracy}%)
            </span>
          </div>
        </div>
      </div>
    );
  };

  // Helper function to render complex stat (like shots breakdown)
  const renderComplexStat = (homeTotal: number, awayTotal: number, homeSuccess: number, awaySuccess: number, label: string, successLabel = 'on target', failLabel = 'off target') => {
    const homeFail = homeTotal - homeSuccess;
    const awayFail = awayTotal - awaySuccess;

    const homeColors = getTeamColors(fixture.teams.home.id);
    const awayColors = getTeamColors(fixture.teams.away.id);

    // Determine which value is higher for highlighting
    const homeIsHigher = homeTotal > awayTotal;
    const awayIsHigher = awayTotal > homeTotal;

    return (
      <div className="mb-6">
        {/* Inline layout with stat values and complex bar */}
        <div className="flex items-center gap-3">
          {/* Home team stat */}
          <div
            className="px-2 py-1 rounded-full text-sm font-bold min-w-[44px] md:min-w-[40px] text-center"
            style={homeIsHigher ? {
              backgroundColor: homeColors.background,
              color: homeColors.text
            } : {}}
          >
            <span className={homeIsHigher ? '' : 'text-foreground dark:text-white'}>
              {homeTotal}
            </span>
          </div>

          {/* Complex stat bar with label */}
          <div className="flex-1 flex flex-col gap-1">
            <span className="text-sm font-medium text-muted-foreground text-center">{label}</span>

            {/* Complex bar showing success vs fail */}
            <div className="flex space-x-1 h-8">
              {/* Home side */}
              <div className="flex-1 flex">
                <div
                  className="flex items-center justify-center text-white text-xs font-bold rounded-l"
                  style={{
                    width: `${(homeFail / (homeTotal || 1)) * 100}%`,
                    backgroundColor: homeColors.background,
                    opacity: 0.7
                  }}
                >
                  {homeFail > 0 && homeFail}
                </div>
                <div
                  className="flex items-center justify-center text-white text-xs font-bold border-l-2 border-white"
                  style={{
                    width: `${(homeSuccess / (homeTotal || 1)) * 100}%`,
                    backgroundColor: homeColors.background
                  }}
                >
                  {homeSuccess > 0 && homeSuccess}
                </div>
              </div>

              {/* Away side */}
              <div className="flex-1 flex">
                <div
                  className="flex items-center justify-center text-white text-xs font-bold border-r-2 border-white"
                  style={{
                    width: `${(awaySuccess / (awayTotal || 1)) * 100}%`,
                    backgroundColor: awayColors.background
                  }}
                >
                  {awaySuccess > 0 && awaySuccess}
                </div>
                <div
                  className="flex items-center justify-center text-white text-xs font-bold rounded-r"
                  style={{
                    width: `${(awayFail / (awayTotal || 1)) * 100}%`,
                    backgroundColor: awayColors.background,
                    opacity: 0.7
                  }}
                >
                  {awayFail > 0 && awayFail}
                </div>
              </div>
            </div>

            {/* Labels */}
            <div className="flex justify-center mt-2 space-x-4 text-xs text-muted-foreground">
              <span>{failLabel}</span>
              <span className="font-medium">{successLabel}</span>
            </div>
          </div>

          {/* Away team stat */}
          <div
            className="px-2 py-1 rounded-full text-sm font-bold min-w-[44px] md:min-w-[40px] text-center"
            style={awayIsHigher ? {
              backgroundColor: awayColors.background,
              color: awayColors.text
            } : {}}
          >
            <span className={awayIsHigher ? '' : 'text-foreground dark:text-white'}>
              {awayTotal}
            </span>
          </div>
        </div>
      </div>
    );
  };



  // Define categorized stats
  const statCategories = {
    general: [
      { key: 'Ball Possession', label: 'Ball possession', isPercentage: true },
      { key: 'Total Shots', label: 'Total shots', isPercentage: false },
      { key: 'expected_goals', label: 'Expected goals (xG)', isPercentage: false },
      { key: 'Free Kicks', label: 'Free kicks', isPercentage: false },
      { key: 'Throw-ins', label: 'Throw-ins', isPercentage: false },
    ],
    attack: [
      { key: 'Total Shots', label: 'Total shots', isPercentage: false },
      { key: 'Shots on Goal', label: 'Shots on target', isPercentage: false },
      { key: 'Shots off Goal', label: 'Shots off target', isPercentage: false },
      { key: 'Corner Kicks', label: 'Corner kicks', isPercentage: false },
      { key: 'Shots insidebox', label: 'Shots inside box', isPercentage: false },
      { key: 'Shots outsidebox', label: 'Shots outside box', isPercentage: false },
      { key: 'Big Chances', label: 'Big chances', isPercentage: false },
      { key: 'Big Chances Missed', label: 'Big chances missed', isPercentage: false },
      { key: 'expected_goals', label: 'Expected goals (xG)', isPercentage: false },
      { key: 'Dangerous Attacks', label: 'Dangerous attacks', isPercentage: false },
      { key: 'Attacks', label: 'Total attacks', isPercentage: false },
    ],
    passing: [
      { key: 'Total passes', label: 'Total passes', isPercentage: false },
      { key: 'Passes accurate', label: 'Accurate passes', isPercentage: false },
      { key: 'Passes %', label: 'Pass accuracy', isPercentage: true },
      { key: 'Key Passes', label: 'Key passes', isPercentage: false },
      { key: 'Crosses', label: 'Crosses', isPercentage: false },
      { key: 'Crosses accurate', label: 'Accurate crosses', isPercentage: false },
      { key: 'Long balls', label: 'Long balls', isPercentage: false },
      { key: 'Long balls accurate', label: 'Accurate long balls', isPercentage: false },
    ],
    defence: [
      { key: 'Offsides', label: 'Offsides', isPercentage: false },
      { key: 'Goalkeeper Saves', label: 'Goalkeeper saves', isPercentage: false },
      { key: 'Blocked Shots', label: 'Blocked shots', isPercentage: false },
      { key: 'Interceptions', label: 'Interceptions', isPercentage: false },
      { key: 'Tackles', label: 'Tackles', isPercentage: false },
      { key: 'Clearances', label: 'Clearances', isPercentage: false },
      { key: 'Duels', label: 'Duels', isPercentage: false },
      { key: 'Duels won', label: 'Duels won', isPercentage: false },
      { key: 'Aerial Duels', label: 'Aerial duels', isPercentage: false },
      { key: 'Aerial Duels won', label: 'Aerial duels won', isPercentage: false },
    ],
    discipline: [
      { key: 'Fouls', label: 'Fouls', isPercentage: false },
      { key: 'Yellow Cards', label: 'Yellow cards', isPercentage: false },
      { key: 'Red Cards', label: 'Red cards', isPercentage: false },
      { key: 'Penalties conceded', label: 'Penalties conceded', isPercentage: false },
    ]
  };

  const renderStatCategory = (categoryName: string, stats: StatConfig[]) => {
    const hasStats = stats.some(statConfig => {
      const homeValue = getStatValue(homeStats.statistics, statConfig.key);
      const awayValue = getStatValue(awayStats.statistics, statConfig.key);
      return homeValue > 0 || awayValue > 0;
    });

    if (!hasStats) return null;

    return (
      <div className="mb-8">
        <div className="text-center text-sm font-bold text-foreground dark:text-white tracking-wide mb-6">
          {categoryName}
        </div>
        <div className="space-y-4">
          {stats.map((statConfig) => {
            let homeValue = getStatValue(homeStats.statistics, statConfig.key);
            let awayValue = getStatValue(awayStats.statistics, statConfig.key);

            // Special handling for "Shots off Goal" - calculate from Total Shots - Shots on Goal
            if (statConfig.key === 'Shots off Goal') {
              const homeTotalShots = getStatValue(homeStats.statistics, 'Total Shots');
              const homeOnTarget = getStatValue(homeStats.statistics, 'Shots on Goal');
              const awayTotalShots = getStatValue(awayStats.statistics, 'Total Shots');
              const awayOnTarget = getStatValue(awayStats.statistics, 'Shots on Goal');

              homeValue = homeTotalShots - homeOnTarget;
              awayValue = awayTotalShots - awayOnTarget;
            }

            // Skip if both values are 0 or null
            if (homeValue === 0 && awayValue === 0) return null;

            // Skip Pass accuracy as it will be combined with Accurate passes
            if (statConfig.key === 'Pass accuracy') return null;

            // Special handling for Accurate passes - combine with Pass accuracy
            if (statConfig.key === 'Accurate passes') {
              const homeAccuracy = getStatValue(homeStats.statistics, 'Pass accuracy');
              const awayAccuracy = getStatValue(awayStats.statistics, 'Pass accuracy');
              return (
                <div key={statConfig.key}>
                  {renderAccuratePassesRow(homeValue, awayValue, homeAccuracy, awayAccuracy)}
                </div>
              );
            }

            // Special handling for duels (show won vs total)
            if (statConfig.key === 'Duels') {
              const homeDuelsWon = getStatValue(homeStats.statistics, 'Duels won');
              const awayDuelsWon = getStatValue(awayStats.statistics, 'Duels won');
              return (
                <div key={statConfig.key}>
                  {renderComplexStat(homeValue, awayValue, homeDuelsWon, awayDuelsWon, statConfig.label, 'won', 'lost')}
                </div>
              );
            }

            // Special handling for aerial duels
            if (statConfig.key === 'Aerial Duels') {
              const homeAerialWon = getStatValue(homeStats.statistics, 'Aerial Duels won');
              const awayAerialWon = getStatValue(awayStats.statistics, 'Aerial Duels won');
              return (
                <div key={statConfig.key}>
                  {renderComplexStat(homeValue, awayValue, homeAerialWon, awayAerialWon, statConfig.label, 'won', 'lost')}
                </div>
              );
            }

            // Use special Ball possession bar for possession stats, simple row for others
            if (statConfig.label === 'Ball possession') {
              return (
                <div key={statConfig.key}>
                  {renderBallPossessionBar(homeValue, awayValue)}
                </div>
              );
            }

            return (
              <div key={statConfig.key}>
                {renderSimpleStatRow(homeValue, awayValue, statConfig.label, statConfig.isPercentage)}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-card rounded-lg border container-border p-6 mx-2 md:mx-0">
      <div className="space-y-6">
        {renderStatCategory('General Stats', statCategories.general)}
        {renderStatCategory('Attack', statCategories.attack)}
        {renderStatCategory('Passing', statCategories.passing)}
        {renderStatCategory('Defence', statCategories.defence)}
        {renderStatCategory('Discipline', statCategories.discipline)}
      </div>
    </div>
  );
}
