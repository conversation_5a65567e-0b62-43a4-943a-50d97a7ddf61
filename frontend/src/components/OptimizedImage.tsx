import Image from 'next/image';
import { useState, useRef, useEffect } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  priority?: boolean;
  className?: string;
  fallbackSrc?: string;
  sizes?: string;
  quality?: number;
  lazy?: boolean; // ✅ Add lazy loading option
}

export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  priority = false,
  className = '',
  fallbackSrc = '/placeholder-team.png',
  sizes,
  quality = 75, // ✅ Reduced default quality for memory savings
  lazy = true, // ✅ Enable lazy loading by default
}: OptimizedImageProps) {
  const [imgSrc, setImgSrc] = useState(src);
  const [isLoading, setIsLoading] = useState(true);
  const [shouldLoad, setShouldLoad] = useState(!lazy || priority);
  const imgRef = useRef<HTMLDivElement>(null);

  // ✅ Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || shouldLoad) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setShouldLoad(true);
            observer.disconnect(); // ✅ Cleanup observer
          }
        });
      },
      {
        rootMargin: '50px', // Load images 50px before they come into view
        threshold: 0.1,
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      observer.disconnect(); // ✅ Cleanup on unmount
    };
  }, [lazy, shouldLoad]);

  const handleError = () => {
    if (imgSrc !== fallbackSrc) {
      setImgSrc(fallbackSrc);
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  return (
    <div ref={imgRef} className={`relative ${className}`} style={{ width, height }}>
      {isLoading && shouldLoad && (
        <div
          className="absolute inset-0 bg-muted animate-pulse rounded"
          style={{ width, height }}
        />
      )}
      {!shouldLoad && (
        <div
          className="absolute inset-0 bg-muted rounded flex items-center justify-center"
          style={{ width, height }}
        >
          <div className="w-4 h-4 bg-muted-foreground/20 rounded" />
        </div>
      )}
      {shouldLoad && (
        <Image
          src={imgSrc}
          alt={alt}
          width={width}
          height={height}
          priority={priority}
          quality={quality}
          sizes={sizes || `${width}px`}
          className={`${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
          onError={handleError}
          onLoad={handleLoad}
          style={{
            objectFit: 'cover',
            width: '100%',
            height: '100%',
          }}
          // ✅ Optimize loading strategy
          loading={priority ? 'eager' : 'lazy'}
          placeholder="blur"
          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R+Rj5m4xbDLLHzLn9+gA/Qp9WuIVnhtnZZEO7L5nP8AZ/8A/9k="
        />
      )}
    </div>
  );
}

// Specialized components for common use cases
export function TeamLogo({ 
  src, 
  alt, 
  size = 32, 
  priority = false,
  className = '' 
}: {
  src: string;
  alt: string;
  size?: number;
  priority?: boolean;
  className?: string;
}) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      priority={priority}
      className={`rounded ${className}`}
      quality={90}
      sizes={`${size}px`}
    />
  );
}

export function LeagueLogo({ 
  src, 
  alt, 
  size = 18, 
  className = '' 
}: {
  src: string;
  alt: string;
  size?: number;
  className?: string;
}) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      priority={false}
      className={`rounded-full ${className}`}
      quality={80}
      sizes={`${size}px`}
    />
  );
}

export function PlayerImage({ 
  src, 
  alt, 
  width = 60, 
  height = 60,
  className = '' 
}: {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
}) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={width}
      height={height}
      priority={false}
      className={`rounded-full ${className}`}
      quality={85}
      sizes={`${width}px`}
    />
  );
}
