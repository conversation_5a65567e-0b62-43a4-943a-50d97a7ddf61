'use client';

import { useEffect, useState } from 'react';

interface MemoryInfo {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}

export default function MemoryMonitor() {
  const [memoryInfo, setMemoryInfo] = useState<MemoryInfo | null>(null);
  const [isVisible, setIsVisible] = useState(true); // ✅ Add visibility toggle

  useEffect(() => {
    // Only run in development
    if (process.env.NODE_ENV !== 'development') return;

    // ✅ Add keyboard shortcut to toggle (Ctrl/Cmd + M)
    const handleKeyPress = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'm') {
        e.preventDefault();
        setIsVisible(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);

    // Cleanup keyboard listener
    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };

    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        const memory = (performance as Performance & { memory: MemoryInfo }).memory;
        setMemoryInfo({
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
        });
      }
    };

    // Update immediately
    updateMemoryInfo();

    // Update every 5 seconds
    const interval = setInterval(updateMemoryInfo, 5000);

    return () => clearInterval(interval);
  }, []);

  if (process.env.NODE_ENV !== 'development' || !memoryInfo || !isVisible) {
    return null;
  }

  const formatBytes = (bytes: number) => {
    return (bytes / 1024 / 1024).toFixed(1) + ' MB';
  };

  const usagePercentage = ((memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100).toFixed(1);

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-3 rounded-lg text-xs font-mono z-50">
      <div className="space-y-1">
        <div className="flex items-center justify-between mb-1">
          <span className="text-gray-300">Memory Monitor</span>
          <span className="text-gray-400 text-[10px]">⌘M to toggle</span>
        </div>
        <div>Memory Usage: {formatBytes(memoryInfo.usedJSHeapSize)}</div>
        <div>Total Heap: {formatBytes(memoryInfo.totalJSHeapSize)}</div>
        <div>Heap Limit: {formatBytes(memoryInfo.jsHeapSizeLimit)}</div>
        <div className={`font-bold ${parseFloat(usagePercentage) > 80 ? 'text-red-400' : parseFloat(usagePercentage) > 60 ? 'text-yellow-400' : 'text-green-400'}`}>
          Usage: {usagePercentage}%
        </div>
      </div>
    </div>
  );
}
