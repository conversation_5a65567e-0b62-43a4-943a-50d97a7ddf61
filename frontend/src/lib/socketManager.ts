'use client';

import { io, Socket } from 'socket.io-client';
import { Fixture } from '@/lib/api';

// Singleton socket manager to prevent multiple connections
class SocketManager {
  private static instance: SocketManager;
  private socket: Socket | null = null;
  private connected = false;
  private subscribers = new Map<string, Set<(data: any) => void>>();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  private constructor() {}

  static getInstance(): SocketManager {
    if (!SocketManager.instance) {
      SocketManager.instance = new SocketManager();
    }
    return SocketManager.instance;
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      const SOCKET_URL = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000';
      
      this.socket = io(`${SOCKET_URL}/fixtures`, {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: false, // ✅ Reuse connections
        autoConnect: true,
      });

      this.socket.on('connect', () => {
        console.log('Socket connected');
        this.connected = true;
        this.reconnectAttempts = 0;
        resolve();
      });

      this.socket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason);
        this.connected = false;
        
        // Auto-reconnect for certain disconnect reasons
        if (reason === 'io server disconnect') {
          // Server initiated disconnect, don't reconnect
          return;
        }
        
        this.attemptReconnect();
      });

      this.socket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        this.connected = false;
        reject(error);
        this.attemptReconnect();
      });

      // Set up event forwarding
      this.setupEventForwarding();
    });
  }

  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    
    setTimeout(() => {
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      this.connect().catch(() => {
        // Reconnection failed, will try again
      });
    }, delay);
  }

  private setupEventForwarding() {
    if (!this.socket) return;

    // Forward all relevant events to subscribers
    const events = [
      'live-fixtures-update',
      'fixture-update',
      'fixture-goal-update',
      'fixture-event',
      'fixture-status-update',
      'league-fixtures-update'
    ];

    events.forEach(event => {
      this.socket!.on(event, (data) => {
        this.notifySubscribers(event, data);
      });
    });
  }

  private notifySubscribers(event: string, data: any) {
    const eventSubscribers = this.subscribers.get(event);
    if (eventSubscribers) {
      eventSubscribers.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in subscriber callback for ${event}:`, error);
        }
      });
    }
  }

  subscribe(event: string, callback: (data: any) => void): () => void {
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, new Set());
    }
    
    this.subscribers.get(event)!.add(callback);

    // Return unsubscribe function
    return () => {
      const eventSubscribers = this.subscribers.get(event);
      if (eventSubscribers) {
        eventSubscribers.delete(callback);
        if (eventSubscribers.size === 0) {
          this.subscribers.delete(event);
        }
      }
    };
  }

  emit(event: string, data?: any) {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    }
  }

  subscribeToLiveFixtures() {
    this.emit('subscribe-live');
  }

  unsubscribeFromLiveFixtures() {
    this.emit('unsubscribe-live');
  }

  subscribeToFixture(fixtureId: number) {
    this.emit('subscribe-fixture', fixtureId);
  }

  unsubscribeFromFixture(fixtureId: number) {
    this.emit('unsubscribe-fixture', fixtureId);
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.connected = false;
      this.subscribers.clear();
    }
  }

  isConnected(): boolean {
    return this.connected;
  }
}

export default SocketManager;
